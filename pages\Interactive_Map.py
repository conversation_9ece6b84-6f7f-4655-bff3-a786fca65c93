import streamlit as st
import pandas as pd
import folium
from geopy.geocoders import Nominatim
from time import sleep
import streamlit.components.v1 as components
import os
import glob
import base64

# Strategic Market Report Text for Delhi
DELHI_STRATEGIC_REPORT = """
Integrated Strategic Market Report for Amaron

Region: Yusuf Sarai Cluster (South Delhi)
Date: June 2025

**1. Geographic, Demographic & Economic Overview**

**Locality Context**

• <PERSON> sits on Sri Aurobindo Marg between AIIMS and Green Park Metro
• Part of South Delhi's urban villages including adjacent zones: Green Park, Gulmohar Park, Hauz Khas Village, Safdarjung Enclave
• Metro connectivity with Green Park Station improves footfall and accessibility

**Demographics & Economy**

• Mixed residential-commercial patronage: high-middle income apartments, university-affiliated students, local businesses
• Significant tech and academic presence: NIFT, NIT, IIFT nearby
• Rising consumer purchasing power with interest in quality electronics, power backup solutions, and lifestyle appliances

**Power & Lifestyle**

• Reliable city-grid but premium users demand UPS/inverter for sensitive loads (AC, computers, Wi‑Fi)
• Frequent internet-savvy, brand-aware consumer base

**2. Electrician & Service Ecosystem**

• Multiple professional electrician services operate via Urban Company and Kaam Ke Bande in Yusuf Sarai and nearby Green Park
• Services include residential, industrial, appliance repair, inverter installations
• High service quality and quick response; electricians are trusted influencers, especially for UPS/inverter purchases

**3. Consumer Behavior & Buying Preferences**

• Urban, tech-aware buyers who value star-rating, brand, digital and offline reviews
• Preference hierarchy: Quality → Energy Efficiency → Brand Reputation → Price
• Willingness to pay premium: ~75% (metro-wise) for reliability and trusted products; financing boosts decisions
• Focus on lifestyle—demand for backup-ready fans, computers, lighting, home appliances
• Purchases driven by bundled assurance: UPS-ready fans, inverter-compatible lights, electrician-backed installations

**4. Retail Landscape: Appliances & Lighting**

┌─────────────────┬───────────┬──────────────┬─────────────────┬──────────────┐
│ Product Category│ Avg Stock │ Annual Demand│ Price Range (₹) │ Margin/Unit  │
├─────────────────┼───────────┼──────────────┼─────────────────┼──────────────┤
│ Ceiling Fans    │    4–6    │  1,200–3,000 │ ₹2,200–₹4,800   │ ₹600–₹900   │
│ Room Lights     │   10–25   │  8,000–10,000│   ₹430–₹950     │ ₹100–₹150   │
│ Light Holders   │   6–50    │ 10,000–12,000│   ₹150–₹950     │  ₹30–₹150   │
│ Desk Lights     │     3     │    ~300      │ ₹800–₹1,200     │ ₹150–₹200   │
│ Solar Lights    │    2–4    │   100–150    │ ₹1,800–₹4,000   │ ₹350–₹600   │
└─────────────────┴───────────┴──────────────┴─────────────────┴──────────────┘

• High turnover in fans and lighting; premium appliance demand supported by frequent urban stacking

**5. Amaron Dealer and Sales Snapshot (Rounded Annual Volumes)**

┌─────────────────────────────────────┬─────────────────────────┬─────┬─────┬───────┬─────┬─────────┐
│ Dealer                              │ Location                │ 2W  │ UPS │ Lubes │ Car │ Tubular │
├─────────────────────────────────────┼─────────────────────────┼─────┼─────┼───────┼─────┼─────────┤
│ Ajay Enterprises (Shri Ganesh)      │ Green Park (110016)     │ 200 │ 200 │   —   │  —  │    —    │
│ Khurana Auto Store (Shri Ganesh)    │ Green Park (110016)     │  30 │ 200 │   —   │  —  │    —    │
│ Motor Agencies Corp (Shri Ganesh)   │ Green Park              │ 100 │  —  │   —   │ 100 │    —    │
│ Shiv Scooter House & NS Automobiles │ Green Park              │  60 │  —  │   —   │  —  │    —    │
│ Park Motors                         │ Safdarjung Enclave      │  —  │  —  │   —   │ 100 │    —    │
│ Balaji Motor Bike Center (KJ)       │ R.K. Puram (110066)     │ 800 │ 900 │   —   │  —  │    —    │
│ Unique India Tyre                   │ R.K. Puram (110066)     │  30 │ 300 │   —   │  —  │    —    │
│ Monto Bike Centre                   │ R.K. Puram (110022)     │ 300 │ 300 │   —   │  —  │    —    │
│ Prince Spares / Rudraksh            │ R.K. Puram cluster      │ 300 │  40 │   —   │  —  │    —    │
└─────────────────────────────────────┴─────────────────────────┴─────┴─────┴───────┴─────┴─────────┘

**Total Volumes:**
• 2W Batteries ~1820 units/year
• UPS ~1940 units/year
• Car Batteries ~200 units/year
• Tubular ~0 (not stocked)

**6. Product Strategy: Core, New, and Non-Core SKUs**

**Core Products**

• Two-Wheeler Batteries: high performance in R.K. Puram, Green Park clusters
• Home UPS/Inverter Batteries: strong traction, especially in electrician-supported areas like Green Park
• Automotive Batteries: Park Motors shows consistent demand

**New Product Expansion**

• UPS-Compatible Ceiling Fans: capitalize on backup-ready consumer segment
• Lighting + UPS Kits: combine existing lighting demand with backup solutions
• Pico-Inverter Modules: affordable option for desk lights, WiFi backup
• Solar-UPS Hybrid Kits: premium fallback for lifestyle consumers and small shops

**Low-Potential Products**

• Tubular Batteries: no current stock nor demand in cluster
• Food Processors & Kitchen GST nodes: appliances overrepresented, mismatch with battery strategy
• Industrial Battery Packs: not suitable for residential/micro-retail environment

**7. Execution Plan & Dealer Ecosystem**

**Initiative | Execution Elements**

Bundled Retail Kits | Ceiling Fan + UPS, Lighting + UPS bundles
Electrician Channel Engagement | Training and incentive programs in Yusuf Sarai, Green Park
Pre-Monsoon & Summer Campaigns | Promote backup bundles during critical seasons
Dealer Financing | Stocking assistance for UPS & battery inventories
Enhanced Brand Visibility | POS branding, local listing on search platforms
Service Support Platforms | Tie-ups with Urban Company electricians

**8. Competitive Landscape: District-Specific**

**Exide**

• Store: Exide Care – Park Motors, Safdarjung Enclave (Arjun Nagar)
• Market strength via legacy dealer presence and strong auto battery lines

**Luminous**

• Widely available in the cluster (e.g., Green Park electronics dealers via InverterWorld)
• Well-positioned in lighting, UPS, and solar categories

**Livguard**

• Dealer listings include Gujarat battery dealers; limited direct footprint in Yusuf Sarai but recognized as alternative brand

**Amaron's Edge**

• Distinct positioning through bundled backup solutions, electrician-driven recommendations, and dealer financing support

**9. Consumer & Market Trends**

• Premium consumers seek reliable, energy-efficient backup systems
• Usage trends show increased digital appliances like computers, UPS-backed networks
• Rapid tech adoption via electricians and organized service platforms to drive sales

**10. Strategic Roadmap**

**Short-Term (6–12 months)**

• Launch core bundled products via Green Park, R.K. Puram dealers
• Activate electrician-led education and demos
• Fast-track outdoor visibility campaigns

**Mid-Term (1–2 years)**

• Introduce solar-UPS hybrid kits and pico-inverters
• Develop packaged solutions for shops and small offices
• Expand service support

**Long-Term (2–3 years)**

• Build Yusuf Sarai cluster as a hub for backup-solutions and energy resiliency
• Pioneer bundled market of lighting, backup, and lifestyle energy products

**Final Summary**

Amaron can build a robust position in the Yusuf Sarai cluster through:

• Leveraging existing high volume of two-wheeler and UPS battery sales
• Rolling out bundled backup solutions suited for this affluent urban micro-market
• Partnering with electricians to drive credibility and installation
• Differentiating from Exide and Luminous via product innovation, financing, and installer support
"""

# Strategic Market Report Text for West Bengal
WEST_BENGAL_STRATEGIC_REPORT = """
Integrated Strategic Market Report for Amaron

Region: North & South 24 Parganas, West Bengal
Date: June 2025

**1. Geographic, Demographic & Economic Overview**

**Population**

• North 24 Parganas: ~11.1 million (approx. 57% urban)
• South 24 Parganas: ~8.2 million (approx. 35% urban)

**Literacy Rate**

• North: ~84%
• South: ~77.5%

**Density**

• North: ~2,960/km²
• South: Significantly lower, predominantly rural

**Per Capita Income**

• State average: ₹141,000 (2023), projected ₹203,000 by 2026
• South 24 Parganas (baseline): ₹56,983 (2013–14)

**Power Infrastructure**

• Full electrification, but rural areas face outages up to 18 hours per day

**Economic Profile**

• North: MSMEs (~18,000), light manufacturing, SEZ zones
• South: Agriculture, aquaculture, proximity to Sundarbans; high solar suitability

**2. Electrician Ecosystem and Consumer Behavior**

**Electrician Availability**

• Strong electrician presence in Barasat, Madhyamgram, Shyamnagar, and Hasnabad
• Install and influence choice of UPS, batteries, lighting systems
• Platforms like Urban Company report high service satisfaction and consistent bookings in urban peripheries

**Consumer Buying Behavior**

• 75% urban consumers and 33% rural consumers are willing to pay premium for energy-efficient appliances
• Key purchase drivers: Quality and durability > Price > Brand > Energy savings
• Rural consumers increasingly invest in lifestyle electronics (TVs, fans, mobile accessories)
• Financing and bundled value perceived positively, especially in Tier 3 locations

**3. Retail Market Landscape – Appliances & Energy Products**

┌─────────────────┬───────────┬──────────────┬─────────────────┬──────────────┐
│ Product Category│ Avg Stock │ Annual Demand│ Retail Price (₹)│ Margin/Unit  │
├─────────────────┼───────────┼──────────────┼─────────────────┼──────────────┤
│ Ceiling Fans    │    2–3    │    50–400    │ ₹2,000–₹4,400   │ ₹500–₹700   │
│ Room Lights     │   4–25    │   200–4,000  │   ₹410–₹1,050   │ ₹100–₹150   │
│ Light Holders   │   2–15    │    25–750    │   ₹150–₹650     │  ₹30–₹150   │
│ Desk Lights     │    1–2    │    10–50     │ ₹800–₹1,200     │    ₹150     │
│ Solar Lights    │    2–4    │   100–150    │ ₹1,800–₹4,000   │ ₹350–₹600   │
│ Food Processors │     1     │    20–50     │ ₹1,500–₹7,500   │ ₹800–₹1,200 │
│ Phone Access.   │   4–12    │   700–1,000  │   ₹150–₹850     │  ₹50–₹150   │
└─────────────────┴───────────┴──────────────┴─────────────────┴──────────────┘

**4. Amaron Dealer Snapshot – Annual Sales Volume (Rounded)**

┌─────────────────────────────────┬─────────────┬────────────┬──────────┬─────────────┬─────────┬─────────────┐
│ Dealer                          │ Location    │ 2W Battery │ Home UPS │ Lubricants  │ Car     │ Tubular     │
├─────────────────────────────────┼─────────────┼────────────┼──────────┼─────────────┼─────────┼─────────────┤
│ Gangotri Enterprise             │ Taki        │     30     │    —     │      —      │    —    │      0      │
│ Zillion Power – Bullet Complex  │ Birlapur    │     90     │    10    │     170     │    —    │     10      │
│ Zillion Power – Senapati Auto   │ Bishnupur   │    100     │    —     │      —      │    —    │      0      │
│ Zillion Power – Dutta Elect.    │ Baruipur    │     50     │    —     │      —      │    —    │     10      │
│ Tirupati Tyre – Nakul Auto      │ Shyamnagar  │     60     │    —     │      40     │    —    │      —      │
│ Pavi Traders – Alam Motors      │ Hasnabad    │     30     │     0    │     120     │     0   │      0      │
└─────────────────────────────────┴─────────────┴────────────┴──────────┴─────────────┴─────────┴─────────────┘

**Estimated Totals:**
• 2W Batteries: ~360 units/year
• Tubular Batteries: ~20 units/year
• Home UPS: ~10 units/year
• Lubricants: ~330 units/year

**5. Product Strategy: What to Sell and What to Avoid**

**High-Potential Products**

• 2W Batteries: Core strength with ready dealer traction
• Tubular Batteries: Medium potential; best sold as lighting/UPS bundles
• Home UPS Systems: High need in high-outage areas; electrician push is critical
• Solar + Battery Hybrid Kits: Strong match for off-grid, disaster-prone zones
• Automotive Lubricants: Strong existing uptake at certain dealers

**Additional Product Opportunities**

• Ceiling Fan + UPS Ready Combos
• Small Solar Home Lighting Kits
• Two-Wheeler Battery + Mobile Charging Add-ons
• Pico Inverter Modules (100W–300W) for rural power gaps

**Low-Potential Products**

• High-end food processors and kitchen appliances: Misaligned with local channel
• Industrial battery systems: Poor infrastructure and limited B2B demand
• Premium car batteries (>₹10,000): Not suitable for current rural purchasing power

**6. Execution Plan with Product Rollout**

┌─────────────────────────────────┬─────────────────────────────────────────────────────┐
│ Initiative                      │ Product Focus                                       │
├─────────────────────────────────┼─────────────────────────────────────────────────────┤
│ Retail Bundling                 │ UPS + lighting kits, ceiling fan combos            │
│ Electrician Network Activation  │ Inverters, solar, battery add-ons                  │
│ Pre-Monsoon Campaign            │ UPS-light kits, energy resilience packs            │
│ Dealer Financing                │ Inventory credit for UPS & battery SKUs            │
│ POS & Branding                  │ Local language posters, in-store visuals           │
│ Digital Visibility              │ Dealer locations on Google, Justdial               │
└─────────────────────────────────┴─────────────────────────────────────────────────────┘

**7. Competitive Landscape in the District**

**Exide**

• Factory: Shyamnagar
• Key Dealers: Exide Care – Lotus (Shyamnagar), Subham Auto Electric (Jagatdal)
• Strong distribution base; legacy trust

**Luminous**

• Key Dealers: S.B. Trading (Ashoknagar), Kanthal Inverter, Solar Fusion
• Active in UPS and solar inverter segments; well distributed in mid-tier appliance outlets

**Livguard**

• Nearby Dealer: Bala Battery, Chichuria (Kolkata fringe)
• Present in West Bengal but weak within 24 Parganas directly

**Amaron Opportunity**

• Differentiate through product bundling, reliability positioning, and local electrician programs

**8. Market Evolution Outlook**

**Short-Term (6–12 months)**

• Activate bundled sales channels in Shyamnagar, Baruipur
• Engage electricians with training and incentives
• Establish brand presence through offline + digital tools

**Mid-Term (1–2 years)**

• Launch solar + UPS + lighting hybrid kits
• Explore institutional sales to MSMEs with small inverter packs

**Long-Term (2–3 years)**

• Become a resilience brand through solar-home systems in off-grid belts
• Collaborate with public schemes to scale hybrid systems (Sundarbans, Hasnabad, Gosaba)

**Final Summary**

Amaron has a high-opportunity growth path in North & South 24 Parganas by leveraging:

• 2W and inverter battery demand via existing dealers
• Strong regional service ecosystems (electricians, tyre shops, lube networks)
• New product bundles tailored for energy reliability
• Avoiding low-ROI SKUs and instead doubling down on scalable energy solutions

A structured rollout integrating electricians, product bundling, and rural solar hybrid kits can help Amaron outcompete Exide's legacy network and Luminous's inverter position.
"""

# Page config
st.set_page_config(
    page_title="Interactive Geographic Intelligence",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Force sidebar to be visible
if 'sidebar_state' not in st.session_state:
    st.session_state.sidebar_state = 'expanded'

# Add logo using Streamlit's native approach
def add_logo_native():
    """Add logo using Streamlit's native image display"""
    import os

    try:
        # Load the new logos (try both paths since we're in pages/)
        stonesbury_path = "../logo/Stonesbury-logo.png"
        adobe_path = "../logo/Adobe Express - file.png"

        if not os.path.exists(stonesbury_path):
            stonesbury_path = "logo/Stonesbury-logo.png"
        if not os.path.exists(adobe_path):
            adobe_path = "logo/Adobe Express - file.png"

        if os.path.exists(stonesbury_path) or os.path.exists(adobe_path):
            # Create columns for layout - logos on opposite sides
            col1, col2, col3 = st.columns([2, 6, 2])

            with col1:
                # Display Stonesbury logo on the left
                if os.path.exists(stonesbury_path):
                    st.markdown('<div style="text-align: left;">', unsafe_allow_html=True)
                    st.image(stonesbury_path, width=120)
                    st.markdown('</div>', unsafe_allow_html=True)

            with col3:
                # Display Mimo (Adobe Express) logo on the right
                if os.path.exists(adobe_path):
                    st.markdown('<div style="text-align: right;">', unsafe_allow_html=True)
                    st.image(adobe_path, width=120)
                    st.markdown('</div>', unsafe_allow_html=True)

            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
        else:
            # Fallback text logos on opposite sides
            col1, col2, col3 = st.columns([2, 6, 2])
            with col1:
                st.markdown("""
                <div style="background: #ff6600; color: white; padding: 15px; border-radius: 8px; text-align: center; font-weight: bold; width: 180px; font-size: 18px;">
                    Stonesbury
                </div>
                """, unsafe_allow_html=True)

            with col3:
                st.markdown("""
                <div style="background: #ff6600; color: white; padding: 15px; border-radius: 8px; text-align: center; font-weight: bold; width: 180px; font-size: 18px; margin-left: auto;">
                    Mimo
                </div>
                """, unsafe_allow_html=True)

            # Add some spacing
            st.markdown("<br>", unsafe_allow_html=True)
    except Exception:
        # Silent fallback - don't disrupt the app if logo fails
        pass

# Add the native logo
add_logo_native()

# Geocoding functions
def get_lat_lon_from_pincode(pincode, geolocator):
    """Get latitude and longitude from PIN code using geocoding"""
    try:
        location = geolocator.geocode(f"{pincode}, Delhi, India")
        if location:
            return pd.Series([location.latitude, location.longitude])
    except Exception as e:
        st.warning(f"Geocoding failed for PIN {pincode}: {str(e)}")
    return pd.Series([None, None])

def add_real_coordinates(df, geolocator):
    """Add real coordinates to dataframe using PIN codes"""
    if 'PIN' not in df.columns:
        return df
    
    # Apply geocoding to get coordinates
    coords = df['PIN'].apply(lambda pin: get_lat_lon_from_pincode(pin, geolocator))
    df['lat'], df['lon'] = coords[0], coords[1]
    
    # Rate limiting for Nominatim API
    sleep(0.5)
    
    # Return only rows with valid coordinates
    return df.dropna(subset=['lat', 'lon'])

# Image Gallery Functions
def get_interactive_images(search_query="", selected_state="", show_all_competitors=False):
    """Get filtered images from interactive_images folder based on search query and state"""
    try:
        images = []
        base_path = "interactive_images"

        if not os.path.exists(base_path):
            return []

        # Determine which state folder to search based on selected_state
        search_folders = []

        if selected_state == "Delhi(NCT)":
            # For Delhi, look specifically in the Delhi folder
            delhi_folder = os.path.join(base_path, "Delhi")
            if os.path.exists(delhi_folder) and os.path.isdir(delhi_folder):
                # Get all subfolders within Delhi folder
                for subfolder in os.listdir(delhi_folder):
                    subfolder_path = os.path.join(delhi_folder, subfolder)
                    if os.path.isdir(subfolder_path):
                        search_folders.append(subfolder_path)
        elif selected_state == "West Bengal":
            # For West Bengal, look specifically in the West Bengal folder
            wb_folder = os.path.join(base_path, "West Bengal")
            if os.path.exists(wb_folder) and os.path.isdir(wb_folder):
                # Get all subfolders within West Bengal folder
                for subfolder in os.listdir(wb_folder):
                    subfolder_path = os.path.join(wb_folder, subfolder)
                    if os.path.isdir(subfolder_path):
                        search_folders.append(subfolder_path)
        else:
            # If no specific state selected or unknown state, search all folders
            for state_folder in os.listdir(base_path):
                state_folder_path = os.path.join(base_path, state_folder)
                if os.path.isdir(state_folder_path):
                    # Get all subfolders within each state folder
                    for subfolder in os.listdir(state_folder_path):
                        subfolder_path = os.path.join(state_folder_path, subfolder)
                        if os.path.isdir(subfolder_path):
                            search_folders.append(subfolder_path)

        # If no specific folders found, fallback to all available folders
        if not search_folders:
            for state_folder in os.listdir(base_path):
                state_folder_path = os.path.join(base_path, state_folder)
                if os.path.isdir(state_folder_path):
                    search_folders.append(state_folder_path)

        # Collect images from relevant folders
        for folder_path in search_folders:
            if os.path.exists(folder_path):
                # Get all image files
                image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp']
                for ext in image_extensions:
                    pattern = os.path.join(folder_path, ext)
                    folder_images = glob.glob(pattern)

                    for img_path in folder_images:
                        # Extract brand from folder name or filename
                        folder_name = os.path.basename(folder_path).lower()
                        filename = os.path.basename(img_path).lower()

                        # Determine brand
                        brand = ""
                        if "amaron" in folder_name or "amaron" in filename:
                            brand = "amaron"
                        elif "exide" in folder_name or "exide" in filename:
                            brand = "exide"
                        elif "tata" in folder_name or "tata" in filename:
                            brand = "tata"

                        # Apply search filter (only if not showing all competitors)
                        if search_query and not show_all_competitors:
                            search_lower = search_query.lower().strip()
                            if search_lower and search_lower not in brand and search_lower not in filename:
                                continue

                        images.append({
                            'path': img_path,
                            'filename': os.path.basename(img_path),
                            'brand': brand,
                            'folder': os.path.basename(folder_path),
                            'state': os.path.basename(os.path.dirname(folder_path)) if os.path.dirname(folder_path) != base_path else 'Unknown'
                        })

        return images

    except Exception as e:
        st.error(f"Error loading images: {str(e)}")
        return []

def encode_image_to_base64(image_path):
    """Convert image to base64 for display"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode()
    except Exception:
        return None

def get_image_metadata(image_path):
    """Extract metadata from image including timestamp and geo-tagging info"""
    import os
    from datetime import datetime

    try:
        # Get file modification time as timestamp
        mod_time = os.path.getmtime(image_path)
        timestamp = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M:%S")

        # Extract location info from folder structure
        path_parts = image_path.replace('\\', '/').split('/')
        state = "Unknown"
        location = "Unknown"

        # Try to extract state and location from path
        if 'interactive_images' in path_parts:
            idx = path_parts.index('interactive_images')
            if idx + 1 < len(path_parts):
                state = path_parts[idx + 1]
            if idx + 2 < len(path_parts):
                location = path_parts[idx + 2]

        # Generate mock coordinates based on state (for demonstration)
        coordinates = "28.5562°N, 77.2095°E"  # Default Delhi coordinates
        if "West Bengal" in state:
            coordinates = "22.4707°N, 88.3617°E"  # West Bengal coordinates
        elif "Delhi" in state:
            coordinates = "28.5562°N, 77.2095°E"  # Delhi coordinates

        return {
            'timestamp': timestamp,
            'coordinates': coordinates,
            'state': state,
            'location': location
        }
    except Exception:
        return {
            'timestamp': 'Unknown',
            'coordinates': 'Unknown',
            'state': 'Unknown',
            'location': 'Unknown'
        }

def create_image_gallery_html(images, search_query="", show_all_competitors=False):
    """Create HTML for the interactive image gallery similar to Dollar Street"""
    if not images:
        return f"""
        <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 10px; margin: 20px 0;">
            <h3 style="color: #666;">No images found</h3>
            <p style="color: #888;">
                {f'No images found for brand "{search_query}"' if search_query else 'No images available in the interactive gallery'}
            </p>
        </div>
        """

    gallery_html = f"""
    <div class="image-gallery-container">
        <style>
            .image-gallery-container {{
                background: transparent;
                border-radius: 15px;
                padding: 0;
                margin: 20px 0;
                box-shadow: none;
            }}
            .gallery-header {{
                text-align: center;
                margin-bottom: 25px;
                padding: 15px 25px;
                background: white;
                border-radius: 10px;
                border: 2px solid #ff6600;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }}
            .gallery-header h3 {{
                color: #ff6600;
                margin: 0 0 10px 0;
                font-size: 24px;
                font-weight: bold;
            }}
            .gallery-stats {{
                color: #666;
                font-size: 14px;
                margin: 5px 0;
            }}
            .image-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 20px;
                margin-top: 20px;
            }}
            .image-item {{
                position: relative;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
                background: white;
            }}
            .image-item:hover {{
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            }}
            .image-thumbnail {{
                width: 100%;
                height: 200px;
                object-fit: cover;
                transition: transform 0.3s ease;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: high-quality;
                -ms-interpolation-mode: bicubic;
                backface-visibility: hidden;
                -webkit-backface-visibility: hidden;
                transform: translateZ(0);
                -webkit-transform: translateZ(0);
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }}
            .image-item:hover .image-thumbnail {{
                transform: scale(1.05);
            }}
            .image-info {{
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
                color: white;
                padding: 15px 12px 12px 12px;
                transform: translateY(100%);
                transition: transform 0.3s ease;
            }}
            .image-item:hover .image-info {{
                transform: translateY(0);
            }}
            .image-brand {{
                font-weight: bold;
                color: #ff6600;
                font-size: 13px;
                text-transform: uppercase;
                margin-bottom: 5px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .image-filename {{
                font-size: 11px;
                color: #fff;
                word-break: break-word;
                margin-bottom: 5px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .image-metadata {{
                font-size: 10px;
                color: #ddd;
                border-top: 1px solid rgba(255, 255, 255, 0.3);
                padding-top: 5px;
                margin-top: 5px;
            }}
            .timestamp {{
                display: flex;
                align-items: center;
                margin-bottom: 2px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .geo-tag {{
                display: flex;
                align-items: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .modal {{
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.9);
            }}
            .modal-content {{
                position: relative;
                margin: auto;
                padding: 20px;
                width: 90%;
                max-width: 800px;
                top: 50%;
                transform: translateY(-50%);
                text-align: center;
            }}
            .modal-image-container {{
                position: relative;
                display: inline-block;
            }}
            .modal-image {{
                max-width: 100%;
                max-height: 70vh;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
                image-rendering: -webkit-optimize-contrast;
                image-rendering: -moz-crisp-edges;
                image-rendering: crisp-edges;
                image-rendering: high-quality;
                -ms-interpolation-mode: bicubic;
                backface-visibility: hidden;
                -webkit-backface-visibility: hidden;
                transform: translateZ(0);
                -webkit-transform: translateZ(0);
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }}
            .modal-info {{
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
                color: white;
                padding: 30px 20px 20px 20px;
                border-radius: 0 0 10px 10px;
                text-align: left;
            }}
            .modal-info h4 {{
                color: #ff6600;
                margin: 0 0 10px 0;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }}
            .modal-info p {{
                color: #fff;
                margin: 0 0 10px 0;
                font-size: 14px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .modal-info div {{
                color: #ddd;
                font-size: 12px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            }}
            .close {{
                position: absolute;
                top: 15px;
                right: 25px;
                color: white;
                font-size: 35px;
                font-weight: bold;
                cursor: pointer;
                z-index: 1001;
            }}
            .close:hover {{
                color: #ff6600;
            }}
            .nav-button {{
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                background: rgba(255, 102, 0, 0.8);
                color: white;
                border: none;
                padding: 15px 20px;
                font-size: 18px;
                cursor: pointer;
                border-radius: 5px;
                transition: background 0.3s ease;
            }}
            .nav-button:hover {{
                background: rgba(255, 102, 0, 1);
            }}
            .prev-button {{
                left: 20px;
            }}
            .next-button {{
                right: 20px;
            }}
            .loading {{
                text-align: center;
                padding: 20px;
                color: #666;
            }}
        </style>

        <div class="gallery-header">
            <h3>📸 Interactive Image Gallery</h3>
            <div class="gallery-stats">
                {len(images)} images found
                {f' • All Competitors' if show_all_competitors else (f' • Brand: "{search_query}"' if search_query else ' • All brands')}
                {f' • State: {list(set([img.get("state", "Unknown") for img in images]))[0]}' if images and len(set([img.get("state", "Unknown") for img in images])) == 1 else ''}
            </div>
        </div>

        <div class="image-grid">
    """

    for i, img in enumerate(images):
        img_base64 = encode_image_to_base64(img['path'])
        if img_base64:
            # Get metadata for the image
            metadata = get_image_metadata(img['path'])

            gallery_html += f"""
            <div class="image-item" onclick="openModal({i})">
                <img src="data:image/jpeg;base64,{img_base64}"
                     alt="{img['filename']}"
                     class="image-thumbnail"
                     loading="lazy">
                <div class="image-info">
                    <div class="image-brand">{img['brand'] or 'General'}</div>
                    <div class="image-filename">{img['filename'][:30]}{'...' if len(img['filename']) > 30 else ''}</div>
                    <div class="image-metadata">
                        <div class="timestamp">🕒 {metadata['timestamp']}</div>
                        <div class="geo-tag">📍 {metadata['coordinates']}</div>
                    </div>
                </div>
            </div>
            """

    gallery_html += """
        </div>

        <!-- Modal for full-size image viewing -->
        <div id="imageModal" class="modal">
            <span class="close" onclick="closeModal()">&times;</span>
            <button class="nav-button prev-button" onclick="changeImage(-1)">&#10094;</button>
            <button class="nav-button next-button" onclick="changeImage(1)">&#10095;</button>
            <div class="modal-content">
                <div class="modal-image-container">
                    <img id="modalImage" class="modal-image" src="" alt="">
                    <div id="modalInfo" class="modal-info">
                        <h4 id="modalBrand"></h4>
                        <p id="modalFilename"></p>
                        <div id="modalMetadata">
                            <div id="modalTimestamp" style="margin-bottom: 5px;"></div>
                            <div id="modalGeoTag"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            let currentImageIndex = 0;
            const imageData = ["""

    # Add image data with base64 encoding and metadata
    image_data_js = []
    for i, img in enumerate(images):
        img_base64 = encode_image_to_base64(img['path'])
        if img_base64:
            metadata = get_image_metadata(img['path'])
            image_data_js.append(f"""
                {{
                    filename: "{img['filename']}",
                    brand: "{img['brand'] or 'General'}",
                    base64: "{img_base64}",
                    folder: "{img['folder']}",
                    state: "{img.get('state', 'Unknown')}",
                    timestamp: "{metadata['timestamp']}",
                    coordinates: "{metadata['coordinates']}"
                }}""")

    gallery_html += ",".join(image_data_js)
    gallery_html += """];

            function openModal(index) {
                currentImageIndex = index;
                const modal = document.getElementById('imageModal');
                const modalImage = document.getElementById('modalImage');
                const modalBrand = document.getElementById('modalBrand');
                const modalFilename = document.getElementById('modalFilename');
                const modalTimestamp = document.getElementById('modalTimestamp');
                const modalGeoTag = document.getElementById('modalGeoTag');

                const img = imageData[index];
                modalImage.src = `data:image/jpeg;base64,${img.base64}`;
                modalBrand.textContent = img.brand;
                modalFilename.textContent = img.filename;
                modalTimestamp.textContent = `🕒 Timestamp: ${img.timestamp}`;
                modalGeoTag.textContent = `📍 Location: ${img.coordinates}`;

                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }

            function closeModal() {
                const modal = document.getElementById('imageModal');
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }

            function changeImage(direction) {
                currentImageIndex += direction;
                if (currentImageIndex >= imageData.length) currentImageIndex = 0;
                if (currentImageIndex < 0) currentImageIndex = imageData.length - 1;

                openModal(currentImageIndex);
            }

            // Keyboard navigation
            document.addEventListener('keydown', function(event) {
                const modal = document.getElementById('imageModal');
                if (modal.style.display === 'block') {
                    if (event.key === 'Escape') {
                        closeModal();
                    } else if (event.key === 'ArrowLeft') {
                        changeImage(-1);
                    } else if (event.key === 'ArrowRight') {
                        changeImage(1);
                    }
                }
            });

            // Close modal when clicking outside the image
            document.getElementById('imageModal').addEventListener('click', function(event) {
                if (event.target === this) {
                    closeModal();
                }
            });
        </script>
    </div>
    """

    return gallery_html

# Live map generation function for Delhi using new CSV data with latitude/longitude
@st.cache_data
def generate_live_delhi_map():
    """Generate an interactive Folium map specifically for Delhi with real dealer locations from Excel file"""
    try:
        # Load Delhi dealer data from the Excel file with latitude/longitude
        try:
            import os
            current_dir = os.getcwd()

            # Check multiple possible locations for the Excel file (prioritize .xls over .xlsx)
            possible_paths = [
                "Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xls",  # Original location - new XLS file
                "YusufSaraiAmaronCentre-FinalCSV.xls",  # Root directory - new XLS file
                os.path.join(current_dir, "Centres", "Delhi", "YusufSaraiAmaronCentre-FinalCSV.xls"),  # Absolute path - new XLS file
                os.path.join(current_dir, "YusufSaraiAmaronCentre-FinalCSV.xls"),  # Absolute path root - new XLS file
                "../Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xls",  # Parent directory path - new XLS file
                "../YusufSaraiAmaronCentre-FinalCSV.xls",  # Parent directory - new XLS file
                "Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx",  # Original location - old XLSX file
                "YusufSaraiAmaronCentre-FinalCSV.xlsx",  # Root directory - old XLSX file
                os.path.join(current_dir, "Centres", "Delhi", "YusufSaraiAmaronCentre-FinalCSV.xlsx"),  # Absolute path - old XLSX file
                os.path.join(current_dir, "YusufSaraiAmaronCentre-FinalCSV.xlsx"),  # Absolute path root - old XLSX file
                "../Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.xlsx",  # Parent directory path - old XLSX file
                "../YusufSaraiAmaronCentre-FinalCSV.xlsx"  # Parent directory - old XLSX file
            ]

            delhi_excel_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    delhi_excel_path = path
                    break

            if not delhi_excel_path:
                st.error(f"❌ Excel file not found in any of these locations: {possible_paths}")
                return None

            # Read Excel file with robust error handling
            try:
                # Handle both .xls and .xlsx files
                if delhi_excel_path.endswith('.xls'):
                    # For .xls files, try xlrd engine first
                    try:
                        delhi_df = pd.read_excel(delhi_excel_path, engine='xlrd')

                    except Exception as xls_error:
                        try:
                            delhi_df = pd.read_excel(delhi_excel_path, engine='openpyxl')

                        except Exception as openpyxl_error:
                            try:
                                delhi_df = pd.read_excel(delhi_excel_path)

                            except Exception as default_error:
                                st.error(f"❌ Failed to read XLS file with any engine:")
                                st.error(f"xlrd error: {str(xls_error)}")
                                st.error(f"openpyxl error: {str(openpyxl_error)}")
                                st.error(f"default error: {str(default_error)}")
                                return None
                else:
                    # For .xlsx files
                    delhi_df = pd.read_excel(delhi_excel_path)

                
            except Exception as e:
                st.error(f"❌ Failed to read Excel file: {str(e)}")
                st.info("💡 Tip: Make sure you have the required Excel libraries installed (openpyxl for .xlsx, xlrd for .xls)")
                return None

            # Remove completely empty rows
            delhi_df = delhi_df.dropna(how='all')

            # Ensure we have the required columns
            required_columns = ['Brand', 'Name', 'latitude', 'longitude']
            missing_columns = [col for col in required_columns if col not in delhi_df.columns]
            if missing_columns:
                st.error(f"❌ Missing required columns: {missing_columns}")
                st.info(f"Available columns: {delhi_df.columns.tolist()}")
                return None

            # Clean and validate latitude/longitude data
            delhi_df['latitude'] = pd.to_numeric(delhi_df['latitude'], errors='coerce')
            delhi_df['longitude'] = pd.to_numeric(delhi_df['longitude'], errors='coerce')

            # Remove rows with invalid coordinates
            valid_coords_df = delhi_df.dropna(subset=['latitude', 'longitude'])

            # Filter for reasonable Delhi coordinates (rough bounds)
            delhi_bounds = {
                'lat_min': 28.4, 'lat_max': 28.8,
                'lon_min': 76.8, 'lon_max': 77.5
            }

            valid_coords_df = valid_coords_df[
                (valid_coords_df['latitude'] >= delhi_bounds['lat_min']) &
                (valid_coords_df['latitude'] <= delhi_bounds['lat_max']) &
                (valid_coords_df['longitude'] >= delhi_bounds['lon_min']) &
                (valid_coords_df['longitude'] <= delhi_bounds['lon_max'])
            ]

            if len(valid_coords_df) == 0:
                st.error("❌ No valid dealer coordinates found in the expected Delhi region")
                return None

        except Exception as e:
            st.error(f"❌ Failed to load Delhi Excel file: {str(e)}")
            return None

        # Calculate map center from actual dealer locations
        center_lat = valid_coords_df['latitude'].mean()
        center_lon = valid_coords_df['longitude'].mean()
        delhi_center = [center_lat, center_lon]

        live_map = folium.Map(
            location=delhi_center,
            zoom_start=14,
            tiles='OpenStreetMap'
        )
        

        # Add Yusuf Sarai market reference marker
        yusuf_sarai_center = [28.5562, 77.2095]
        folium.Marker(
            location=yusuf_sarai_center,
            popup=folium.Popup(
                """
                <div style='width: 250px'>
                    <h4 style='color: #ff6600; margin: 0;'>📍 Yusuf Sarai Market</h4>
                    <p><b>Location:</b> South Delhi<br>
                    <b>Geography:</b> South Central<br>
                    <b>Type:</b> Commercial Hub<br>
                    <b>Specialties:</b> Electronics, Home Appliances</p>
                </div>
                """,
                max_width=300
            ),
            icon=folium.Icon(color='orange', icon='star', prefix='fa')
        ).add_to(live_map)

        # Add all dealer markers using actual coordinates from CSV
        markers_added = 0
        for idx, row in valid_coords_df.iterrows():
            try:
                # Get coordinates directly from CSV
                lat = row['latitude']
                lon = row['longitude']

                # Get dealer information
                dealer_name = row.get('Name', f'Dealer {idx+1}')
                dealer_address = row.get('Address', 'Delhi')
                dealer_pin = row.get('PIN', 'N/A')
                dealer_brand = row.get('Brand', 'AMARON')

                # Create dealer popup with information from CSV
                dealer_info = f"""
                <div style='width: 300px'>
                    <h4 style='color: red; margin: 0 0 8px 0;'>🏪 {dealer_brand} Dealer</h4>
                    <p style='margin: 0; font-size: 12px; line-height: 1.4;'>
                        <b>Name:</b> {dealer_name[:60]}{'...' if len(str(dealer_name)) > 60 else ''}<br>
                        <b>PIN:</b> {dealer_pin}<br>
                        <b>Coordinates:</b> {lat:.6f}, {lon:.6f}<br>
                        <b>Address:</b> {dealer_address[:100]}{'...' if len(str(dealer_address)) > 100 else ''}<br>
                        <b>Source:</b> Live CSV Data
                    </p>
                </div>
                """

                # Add marker to map
                folium.Marker(
                    location=(lat, lon),
                    popup=folium.Popup(dealer_info, max_width=320),
                    icon=folium.Icon(color='red', icon='flash', prefix='fa')
                ).add_to(live_map)
                markers_added += 1

            except Exception as e:
                st.warning(f"⚠️ Failed to add marker for row {idx}: {str(e)}")
                continue

        # Total markers is the number we successfully added from CSV
        total_markers = markers_added

        # Add circle showing trade area around the center
        folium.Circle(
            location=delhi_center,
            radius=3000,  # 3km radius to cover all dealers
            popup=f'Delhi Trade Area Coverage<br>({total_markers} dealers from CSV - Live Generated)',
            color='orange',
            fill=True,
            fillOpacity=0.1
        ).add_to(live_map)

        # Add layer control
        folium.LayerControl().add_to(live_map)

        # Add info marker about live generation
        folium.Marker(
            [center_lat + 0.02, center_lon],  # Position slightly above center
            popup=f"🗺️ Live Generated Delhi Map<br>{total_markers} dealers with real coordinates from CSV",
            icon=folium.DivIcon(
                html=f'<div style="background: white; border: 2px solid orange; padding: 5px; border-radius: 5px; font-size: 12px; font-weight: bold;">📊 CSV Data: {total_markers} dealers</div>',
                icon_size=(140, 30)
            )
        ).add_to(live_map)

        return live_map._repr_html_()

    except Exception as e:
        st.error(f"Error generating live Delhi map: {str(e)}")
        return None

# Live map generation function for West Bengal using CSV data with latitude/longitude
@st.cache_data
def generate_live_west_bengal_map():
    """Generate an interactive Folium map specifically for West Bengal with real dealer locations from CSV"""
    try:
        # Load West Bengal dealer data from the CSV file with latitude/longitude
        try:
            import os
            current_dir = os.getcwd()

            # Check multiple possible locations for the CSV file
            possible_paths = [
                "Centres/West bengal/South24ParganasAmaronCenter-CSV.csv",  # Original location
                "Centres/West Bengal/South24ParganasAmaronCenter-CSV.csv",  # Alternative spelling
                os.path.join(current_dir, "Centres", "West bengal", "South24ParganasAmaronCenter-CSV.csv"),
                os.path.join(current_dir, "Centres", "West Bengal", "South24ParganasAmaronCenter-CSV.csv"),
                "../Centres/West bengal/South24ParganasAmaronCenter-CSV.csv",
                "../Centres/West Bengal/South24ParganasAmaronCenter-CSV.csv"
            ]

            wb_csv_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    wb_csv_path = path
                    break

            if not wb_csv_path:
                st.error(f"❌ CSV file not found in any of these locations: {possible_paths}")
                return None

            # Read CSV with robust error handling and encoding support
            try:
                # Try different encodings to handle special characters
                encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1', 'utf-16']
                wb_df = None
                successful_encoding = None
                
                for encoding in encodings_to_try:
                    try:
                        wb_df = pd.read_csv(wb_csv_path, encoding=encoding)
                        successful_encoding = encoding
                        break
                    except (UnicodeDecodeError, UnicodeError):
                        continue
                    except Exception as e:
                        if encoding == encodings_to_try[-1]:  # Last encoding attempt
                            raise e
                        continue
                
                if wb_df is None:
                    st.error(f"❌ Failed to read CSV file with any encoding: {encodings_to_try}")
                    return None
            except Exception as e:
                st.error(f"❌ Failed to read CSV file: {str(e)}")
                return None

            # Remove completely empty rows
            wb_df = wb_df.dropna(how='all')

            # Ensure we have the required columns
            required_columns = ['Brand', 'Name', 'latitude', 'longitude']
            missing_columns = [col for col in required_columns if col not in wb_df.columns]
            if missing_columns:
                st.error(f"❌ Missing required columns: {missing_columns}")
                st.info(f"Available columns: {wb_df.columns.tolist()}")
                return None

            # Clean and validate latitude/longitude data
            wb_df['latitude'] = pd.to_numeric(wb_df['latitude'], errors='coerce')
            wb_df['longitude'] = pd.to_numeric(wb_df['longitude'], errors='coerce')

            # Remove rows with invalid coordinates
            valid_coords_df = wb_df.dropna(subset=['latitude', 'longitude'])

            # Filter for reasonable West Bengal coordinates (rough bounds)
            wb_bounds = {
                'lat_min': 21.5, 'lat_max': 27.5,
                'lon_min': 85.0, 'lon_max': 90.0
            }

            valid_coords_df = valid_coords_df[
                (valid_coords_df['latitude'] >= wb_bounds['lat_min']) &
                (valid_coords_df['latitude'] <= wb_bounds['lat_max']) &
                (valid_coords_df['longitude'] >= wb_bounds['lon_min']) &
                (valid_coords_df['longitude'] <= wb_bounds['lon_max'])
            ]

            if len(valid_coords_df) == 0:
                st.error("❌ No valid dealer coordinates found in the expected West Bengal region")
                return None

        except Exception as e:
            st.error(f"❌ Failed to load West Bengal CSV file: {str(e)}")
            return None

        # Calculate map center from actual dealer locations
        center_lat = valid_coords_df['latitude'].mean()
        center_lon = valid_coords_df['longitude'].mean()
        wb_center = [center_lat, center_lon]

        live_map = folium.Map(
            location=wb_center,
            zoom_start=12,
            tiles='OpenStreetMap'
        )

        # Add South 24 Parganas reference marker
        s24p_center = [22.4707, 88.3617]  # South 24 Parganas coordinates
        folium.Marker(
            location=s24p_center,
            popup=folium.Popup(
                """
                <div style='width: 250px'>
                    <h4 style='color: #ff6600; margin: 0;'>📍 South 24 Parganas</h4>
                    <p><b>Location:</b> West Bengal<br>
                    <b>Geography:</b> South East<br>
                    <b>Type:</b> Commercial District<br>
                    <b>Specialties:</b> Electronics, Automotive</p>
                </div>
                """,
                max_width=300
            ),
            icon=folium.Icon(color='orange', icon='star', prefix='fa')
        ).add_to(live_map)

        # Add all dealer markers using actual coordinates from CSV
        markers_added = 0
        for idx, row in valid_coords_df.iterrows():
            try:
                # Get coordinates directly from CSV
                lat = row['latitude']
                lon = row['longitude']

                # Get dealer information
                dealer_name = row.get('Name', f'Dealer {idx+1}')
                dealer_address = row.get('Address', 'West Bengal')
                dealer_pin = row.get('PIN', 'N/A')
                dealer_brand = row.get('Brand', 'AMARON')

                # Create dealer popup with information from CSV
                dealer_info = f"""
                <div style='width: 300px'>
                    <h4 style='color: red; margin: 0 0 8px 0;'>🏪 {dealer_brand} Dealer</h4>
                    <p style='margin: 0; font-size: 12px; line-height: 1.4;'>
                        <b>Name:</b> {dealer_name[:60]}{'...' if len(str(dealer_name)) > 60 else ''}<br>
                        <b>PIN:</b> {dealer_pin}<br>
                        <b>Coordinates:</b> {lat:.6f}, {lon:.6f}<br>
                        <b>Address:</b> {dealer_address[:100]}{'...' if len(str(dealer_address)) > 100 else ''}<br>
                        <b>Source:</b> Live CSV Data
                    </p>
                </div>
                """

                # Add marker to map
                folium.Marker(
                    location=(lat, lon),
                    popup=folium.Popup(dealer_info, max_width=320),
                    icon=folium.Icon(color='red', icon='flash', prefix='fa')
                ).add_to(live_map)
                markers_added += 1

            except Exception as e:
                st.warning(f"⚠️ Failed to add marker for row {idx}: {str(e)}")
                continue

        # Total markers is the number we successfully added from CSV
        total_markers = markers_added

        # Add circle showing trade area around the center
        folium.Circle(
            location=wb_center,
            radius=8000,  # 8km radius to cover all dealers
            popup=f'West Bengal Trade Area Coverage<br>({total_markers} dealers from CSV - Live Generated)',
            color='orange',
            fill=True,
            fillOpacity=0.1
        ).add_to(live_map)

        # Add layer control
        folium.LayerControl().add_to(live_map)

        # Add info marker about live generation
        folium.Marker(
            [center_lat + 0.05, center_lon],  # Position slightly above center
            popup=f"🗺️ Live Generated West Bengal Map<br>{total_markers} dealers with real coordinates from CSV",
            icon=folium.DivIcon(
                html=f'<div style="background: white; border: 2px solid orange; padding: 5px; border-radius: 5px; font-size: 12px; font-weight: bold;">📊 CSV Data: {total_markers} dealers</div>',
                icon_size=(140, 30)
            )
        ).add_to(live_map)

        return live_map._repr_html_()

    except Exception as e:
        st.error(f"Error generating live West Bengal map: {str(e)}")
        return None

# Custom CSS for the map page
st.markdown("""
    <style>
        html, body, [data-testid="stApp"] {
            background-color: #fff3e0 !important;
            color: #333 !important;
        }

        /* Main content area background */
        .main .block-container {
            background-color: #fff3e0 !important;
            padding-left: 20px !important;
        }

        .stSidebar {
            background-color: #e8f4f8 !important;
            border-right: 3px solid #ff6600 !important;
            margin-right: 10px !important;
            display: block !important;
            visibility: visible !important;
        }

        .stSidebar > div {
            background-color: #e8f4f8 !important;
            display: block !important;
            visibility: visible !important;
        }

        .stSidebar .stCheckbox > label {
            color: #333 !important;
            font-weight: normal !important;
            visibility: visible !important;
        }

        .stSidebar .stCheckbox > label > div {
            color: #333 !important;
            visibility: visible !important;
        }

        .stSidebar label {
            color: #333 !important;
            visibility: visible !important;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #ff6600 !important;
        }

        .map-info {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ff6600;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
""", unsafe_allow_html=True)

# Page header - dynamic based on selected state
selected_state = getattr(st.session_state, 'selected_map_state', None)
if selected_state == "Delhi(NCT)":
    st.title("🗺️ Delhi Geographic Intelligence")
else:
    st.title("🗺️ Interactive Geographic Intelligence")

# Display selected state information if available
if hasattr(st.session_state, 'selected_map_state') and st.session_state.selected_map_state:
    selected_state = st.session_state.selected_map_state
    selected_geography = getattr(st.session_state, 'selected_map_geography', 'South Central')
    selected_locality = getattr(st.session_state, 'selected_map_locality', 'Yusuf Sarai')

    st.markdown(f"""
    <div style="background: linear-gradient(145deg, #fff3e0, #ffffff); padding: 15px; border-radius: 10px; border: 2px solid #ff6600; margin: 10px 0;">
        <h4 style="color: #ff6600; margin: 0 0 10px 0;">📍 Selected Location</h4>
        <p style="margin: 0; color: #333;">
            <strong>State:</strong> {selected_state} |
            <strong>Geography:</strong> {selected_geography} |
            <strong>Locality:</strong> {selected_locality}
        </p>
    </div>
    """, unsafe_allow_html=True)

    st.markdown(f"*Live dealer network visualization and trade area analysis for {selected_locality}, {selected_state}*")
else:
    st.markdown("*Live dealer network visualization and trade area analysis for Yusuf Sarai market*")

# Sidebar controls
st.sidebar.markdown("""
<style>
.stSidebar .stCheckbox label {
    color: #333 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}
.stSidebar .stCheckbox label span {
    color: #333 !important;
}
</style>
""", unsafe_allow_html=True)

# Use a more explicit approach for the checkbox
st.sidebar.markdown("**Show Competitors:**")
show_competitors = st.sidebar.checkbox(
    "Show Competitors",
    value=False,
    key="show_competitors_checkbox"
)

# Check if Delhi is selected and display appropriate map
if selected_state == "Delhi(NCT)":
    # Add regenerate button
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("🔄 Regenerate Map", help="Generate a fresh map with live geocoding"):
            st.cache_data.clear()  # Clear cache to force regeneration
            st.rerun()

    # Display live generated Delhi map
    with st.spinner("🔄 Generating live Delhi map with real geocoding..."):
        live_delhi_html = generate_live_delhi_map()

    if live_delhi_html:
        st.markdown("### 🗺️ Live Delhi Dealer Map")
        st.markdown("*Real-time generated map with live geocoding of dealer locations from YusufSaraiAmaronCentre-FinalCSV.csv*")
        components.html(live_delhi_html, height=700)
    else:
        st.error("❌ Failed to generate live Delhi map from your CSV data.")
        st.markdown("""
        ### 🔧 Troubleshooting Delhi Map Generation

        The live map generation failed. This could be due to:
        - Network connectivity issues with geocoding API
        - Invalid PIN codes in the Delhi CSV file
        - Geocoding API rate limits
        - Missing CSV file: `Centres/Delhi/YusufSaraiAmaronCentre-FinalCSV.csv`

        **Please try:**
        1. Click the "🔄 Regenerate Map" button to retry
        2. Check your internet connection
        3. Wait a few minutes and try again (API rate limits)
        4. Verify the CSV file exists and has valid data
        """)

else:
    # Display the live Folium map for West Bengal
    # Add regenerate button for West Bengal
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("🔄 Regenerate Map", help="Generate a fresh map with live geocoding", key="wb_regenerate"):
            st.cache_data.clear()  # Clear cache to force regeneration
            st.rerun()

    with st.spinner("🔄 Generating live West Bengal map with real geocoding..."):
        live_wb_html = generate_live_west_bengal_map()

    if live_wb_html:
        st.markdown("### 🗺️ Live West Bengal Dealer Map")
        st.markdown("*Real-time generated map with live geocoding of dealer locations from South24ParganasAmaronCenter-CSV.csv*")
        components.html(live_wb_html, height=700)

# Interactive Image Gallery Section
st.markdown("---")
st.markdown("### 📸 Interactive Image Gallery")

# Get search query from session state if available
search_query = getattr(st.session_state, 'map_search_query', '')
if search_query and not show_competitors:
    st.markdown(f"*Showing images for brand: **{search_query}***")
elif search_query and show_competitors:
    st.markdown(f"*Showing all competitor images (including **{search_query}**)*")
elif show_competitors:
    st.markdown("*Showing all competitor images*")

# Load and display images
with st.spinner("🔄 Loading interactive image gallery..."):
    images = get_interactive_images(search_query, selected_state, show_competitors)

if images:
    # Create and display the gallery
    gallery_html = create_image_gallery_html(images, search_query, show_competitors)
    components.html(gallery_html, height=800, scrolling=True)
else:
    # Show message when no images are found
    if show_competitors:
        st.info("🔍 No competitor images found")
        st.markdown("""
        **Try:**
        - Checking if images exist in the interactive_images folder
        - Verifying the selected state has available images
        - Ensuring image files are in supported formats (JPG, JPEG, PNG, GIF, BMP)
        """)
    elif search_query:
        st.info(f"🔍 No images found for brand: **{search_query}**")
        st.markdown("""
        **Try:**
        - Searching for different brand names (Amaron, Exide, Tata)
        - Enabling "Show Competitors" to see all available images
        - Checking if images exist for the selected state and brand
        """)
    else:
        st.info("📁 No images available in the interactive gallery")
        st.markdown("""
        **Expected image location:** `interactive_images/` folder with brand-specific subfolders

        **Supported formats:** JPG, JPEG, PNG, GIF, BMP
        """)

# Function to convert ASCII tables to HTML tables
def convert_ascii_table_to_html(table_lines):
    """Convert ASCII table lines to HTML table"""
    if not table_lines:
        return ""

    # Find header row (contains │ characters)
    header_row = None
    data_rows = []

    for line in table_lines:
        if '│' in line and not line.startswith('├') and not line.startswith('└') and not line.startswith('┌'):
            cells = [cell.strip() for cell in line.split('│')[1:-1]]  # Remove first and last empty elements
            if header_row is None:
                header_row = cells
            else:
                data_rows.append(cells)

    if not header_row:
        return '\n'.join(table_lines)  # Return original if can't parse

    # Generate HTML table
    html = '<table style="width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 14px;">\n'

    # Header
    html += '<thead><tr style="background-color: #ff6600; color: white;">\n'
    for cell in header_row:
        html += f'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">{cell}</th>\n'
    html += '</tr></thead>\n'

    # Data rows
    html += '<tbody>\n'
    for i, row in enumerate(data_rows):
        bg_color = '#f9f9f9' if i % 2 == 0 else 'white'
        html += f'<tr style="background-color: {bg_color};">\n'
        for cell in row:
            html += f'<td style="border: 1px solid #ddd; padding: 8px;">{cell}</td>\n'
        html += '</tr>\n'
    html += '</tbody>\n'
    html += '</table>\n'

    return html

# Function to format the strategic report with proper formatting and HTML tables
def format_strategic_report_with_lines(text):
    """Format the strategic report text with horizontal lines between sections, proper bullet points, and HTML tables"""
    lines = text.strip().split('\n')
    formatted_lines = []
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        if not line:
            i += 1
            continue  # Skip empty lines to remove gaps

        # Check if this is the start of an ASCII table
        if line.startswith('┌'):
            # Collect all table lines
            table_lines = []
            while i < len(lines) and (lines[i].strip().startswith(('┌', '├', '│', '└')) or not lines[i].strip()):
                if lines[i].strip():  # Only add non-empty lines
                    table_lines.append(lines[i].strip())
                i += 1

            # Convert to HTML table
            html_table = convert_ascii_table_to_html(table_lines)
            formatted_lines.append(html_table)
            continue

        # Check if this is a numbered section header (e.g., "1. Geographic...")
        if line.startswith('**') and any(char.isdigit() for char in line[:10]):
            if formatted_lines:  # Add separator before new section (except first)
                formatted_lines.append('─' * 100)
            formatted_lines.append(line.replace('**', '').strip())
            formatted_lines.append('')  # Add gap after main section headers

        # Check if this is a subsection header (e.g., "Locality Context")
        elif line.startswith('**') and not any(char.isdigit() for char in line[:10]):
            formatted_lines.append(line.replace('**', '').strip())
            formatted_lines.append('')  # Add gap after subsection headers

        # Regular content lines - add bullet points for descriptive content
        else:
            # Check if this looks like a list item (descriptive content)
            if (line and len(line) > 15 and
                not line.startswith('Region:') and
                not line.startswith('Date:') and
                not line.startswith('Total Volumes:') and
                not line.startswith('2W Batteries') and
                not line.startswith('UPS ~') and
                not line.startswith('Car Batteries') and
                not line.startswith('Tubular ~') and
                i > 0 and i-1 < len(lines) and lines[i-1].strip().endswith(':')):
                formatted_lines.append('• ' + line)
            else:
                formatted_lines.append(line)

        i += 1

    return '\n'.join(formatted_lines)

# Sample Text Area Section - Only show for Delhi when accessed via brand search
st.markdown("---")
st.markdown("### 📄 Strategic Market Analysis")

# Check if this is Delhi and accessed via brand search (Open button)
selected_state = getattr(st.session_state, 'selected_map_state', None)
search_query = getattr(st.session_state, 'map_search_query', '')

if selected_state == "Delhi(NCT)":
    # Add custom CSS for better text area styling
    st.markdown("""
    <style>
    .stTextArea > div > div > textarea {
        background-color: white !important;
        color: #000000 !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        border: 2px solid #ff6600 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
    }

    /* Remove any selection highlighting */
    .stTextArea > div > div > textarea::selection {
        background-color: transparent !important;
        color: #000000 !important;
    }

    .stTextArea > div > div > textarea::-moz-selection {
        background-color: transparent !important;
        color: #000000 !important;
    }

    /* Ensure text is black in all states */
    .stTextArea textarea {
        color: #000000 !important;
        background-color: white !important;
    }

    /* Override any global text color settings */
    div[data-testid="stTextArea"] textarea {
        color: #000000 !important;
        background-color: white !important;
    }

    /* Force text color for disabled textarea */
    .stTextArea textarea[disabled] {
        color: #000000 !important;
        background-color: white !important;
        opacity: 1 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Format the strategic report with horizontal lines
    formatted_report = format_strategic_report_with_lines(DELHI_STRATEGIC_REPORT)

    # Display the strategic report using markdown in a styled container
    st.markdown("**Integrated Strategic Market Report for Amaron - Yusuf Sarai Cluster:**")

    # Add CSS for enhanced scrollbar
    st.markdown("""
    <style>
    .strategic-report-container {
        background: linear-gradient(145deg, #fff3e0, #ffffff);
        color: #000000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 16px;
        line-height: 1.8;
        border: 2px solid #ff6600;
        border-radius: 8px;
        padding: 20px;
        height: 600px;
        overflow-y: auto;
        white-space: pre-line;
        margin: 10px 0;
        outline: none;
    }

    /* Enhanced scrollbar styling */
    .strategic-report-container::-webkit-scrollbar {
        width: 16px !important;
        background-color: #f1f1f1;
    }

    .strategic-report-container::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        border-radius: 8px;
    }

    .strategic-report-container::-webkit-scrollbar-thumb {
        background-color: #ff6600;
        border-radius: 8px;
        border: 2px solid #f1f1f1;
    }

    .strategic-report-container::-webkit-scrollbar-thumb:hover {
        background-color: #e55a00;
    }

    .strategic-report-container::-webkit-scrollbar-thumb:active {
        background-color: #cc4d00;
    }
    </style>
    """, unsafe_allow_html=True)

    # Create the report container
    st.markdown(f"""
    <div id="delhi-strategic-report" class="strategic-report-container" tabindex="0">
{formatted_report}
    </div>
    """, unsafe_allow_html=True)

    # Add JavaScript for keyboard navigation
    st.markdown("""
    <script>
    (function() {
        // Add keyboard navigation for Delhi strategic report
        function setupDelhiNavigation() {
            const delhiContainer = document.getElementById('delhi-strategic-report');
            if (delhiContainer && !delhiContainer.hasAttribute('data-nav-setup')) {
                delhiContainer.setAttribute('data-nav-setup', 'true');

                delhiContainer.addEventListener('keydown', function(e) {
                    const scrollAmount = 50;
                    switch(e.key) {
                        case 'ArrowUp':
                            e.preventDefault();
                            this.scrollTop -= scrollAmount;
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.scrollTop += scrollAmount;
                            break;
                        case 'PageUp':
                            e.preventDefault();
                            this.scrollTop -= this.clientHeight * 0.8;
                            break;
                        case 'PageDown':
                            e.preventDefault();
                            this.scrollTop += this.clientHeight * 0.8;
                            break;
                        case 'Home':
                            e.preventDefault();
                            this.scrollTop = 0;
                            break;
                        case 'End':
                            e.preventDefault();
                            this.scrollTop = this.scrollHeight;
                            break;
                    }
                });

                // Make container focusable and add focus indicator
                delhiContainer.addEventListener('focus', function() {
                    this.style.boxShadow = '0 0 0 3px rgba(255, 102, 0, 0.3)';
                });

                delhiContainer.addEventListener('blur', function() {
                    this.style.boxShadow = 'none';
                });
            }
        }

        // Try to setup immediately
        setupDelhiNavigation();

        // Also try after DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupDelhiNavigation);
        }

        // And try after a short delay for Streamlit
        setTimeout(setupDelhiNavigation, 100);
    })();
    </script>
    """, unsafe_allow_html=True)

elif selected_state == "West Bengal":
    # Add custom CSS for better text area styling
    st.markdown("""
    <style>
    .stTextArea > div > div > textarea {
        background-color: white !important;
        color: #000000 !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.6 !important;
        border: 2px solid #ff6600 !important;
        border-radius: 8px !important;
        padding: 20px !important;
        user-select: text !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
    }

    /* Remove any selection highlighting */
    .stTextArea > div > div > textarea::selection {
        background-color: transparent !important;
        color: #000000 !important;
    }

    .stTextArea > div > div > textarea::-moz-selection {
        background-color: transparent !important;
        color: #000000 !important;
    }

    /* Ensure text is black in all states */
    .stTextArea textarea {
        color: #000000 !important;
        background-color: white !important;
    }

    /* Override any global text color settings */
    div[data-testid="stTextArea"] textarea {
        color: #000000 !important;
        background-color: white !important;
    }

    /* Force text color for disabled textarea */
    .stTextArea textarea[disabled] {
        color: #000000 !important;
        background-color: white !important;
        opacity: 1 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Format the strategic report with horizontal lines
    formatted_report = format_strategic_report_with_lines(WEST_BENGAL_STRATEGIC_REPORT)

    # Display the strategic report using markdown in a styled container
    st.markdown("**Integrated Strategic Market Report for Amaron - North & South 24 Parganas:**")

    # Add CSS for enhanced scrollbar (West Bengal)
    st.markdown("""
    <style>
    .strategic-report-container-wb {
        background: linear-gradient(145deg, #fff3e0, #ffffff);
        color: #000000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 16px;
        line-height: 1.8;
        border: 2px solid #ff6600;
        border-radius: 8px;
        padding: 20px;
        height: 600px;
        overflow-y: auto;
        white-space: pre-line;
        margin: 10px 0;
        outline: none;
    }

    /* Enhanced scrollbar styling for West Bengal */
    .strategic-report-container-wb::-webkit-scrollbar {
        width: 16px !important;
        background-color: #f1f1f1;
    }

    .strategic-report-container-wb::-webkit-scrollbar-track {
        background-color: #f1f1f1;
        border-radius: 8px;
    }

    .strategic-report-container-wb::-webkit-scrollbar-thumb {
        background-color: #ff6600;
        border-radius: 8px;
        border: 2px solid #f1f1f1;
    }

    .strategic-report-container-wb::-webkit-scrollbar-thumb:hover {
        background-color: #e55a00;
    }

    .strategic-report-container-wb::-webkit-scrollbar-thumb:active {
        background-color: #cc4d00;
    }
    </style>
    """, unsafe_allow_html=True)

    # Create the report container
    st.markdown(f"""
    <div id="wb-strategic-report" class="strategic-report-container-wb" tabindex="0">
{formatted_report}
    </div>
    """, unsafe_allow_html=True)

    # Add JavaScript for keyboard navigation
    st.markdown("""
    <script>
    (function() {
        // Add keyboard navigation for West Bengal strategic report
        function setupWBNavigation() {
            const wbContainer = document.getElementById('wb-strategic-report');
            if (wbContainer && !wbContainer.hasAttribute('data-nav-setup')) {
                wbContainer.setAttribute('data-nav-setup', 'true');

                wbContainer.addEventListener('keydown', function(e) {
                    const scrollAmount = 50;
                    switch(e.key) {
                        case 'ArrowUp':
                            e.preventDefault();
                            this.scrollTop -= scrollAmount;
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            this.scrollTop += scrollAmount;
                            break;
                        case 'PageUp':
                            e.preventDefault();
                            this.scrollTop -= this.clientHeight * 0.8;
                            break;
                        case 'PageDown':
                            e.preventDefault();
                            this.scrollTop += this.clientHeight * 0.8;
                            break;
                        case 'Home':
                            e.preventDefault();
                            this.scrollTop = 0;
                            break;
                        case 'End':
                            e.preventDefault();
                            this.scrollTop = this.scrollHeight;
                            break;
                    }
                });

                // Make container focusable and add focus indicator
                wbContainer.addEventListener('focus', function() {
                    this.style.boxShadow = '0 0 0 3px rgba(255, 102, 0, 0.3)';
                });

                wbContainer.addEventListener('blur', function() {
                    this.style.boxShadow = 'none';
                });
            }
        }

        // Try to setup immediately
        setupWBNavigation();

        // Also try after DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', setupWBNavigation);
        }

        // And try after a short delay for Streamlit
        setTimeout(setupWBNavigation, 100);
    })();
    </script>
    """, unsafe_allow_html=True)

else:
    # Show placeholder for other states or when no state is selected
    st.markdown("**Strategic Market Analysis - Stonesbury AI:**")
    st.markdown("""
    <div style="
        background: linear-gradient(145deg, #fff3e0, #ffffff);
        color: #000000;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        border: 2px solid #ff6600;
        border-radius: 8px;
        padding: 20px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin: 10px 0;
    ">
Strategic market analysis is available for Delhi and West Bengal regions. Please select a state from the main page and click the 'Open' button in the brand search section to view the detailed report.
    </div>
    """, unsafe_allow_html=True)